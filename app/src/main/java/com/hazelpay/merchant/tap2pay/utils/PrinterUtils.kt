package com.hazelpay.merchant.tap2pay.utils

import android.content.Context
import android.print.PrintAttributes
import android.print.PrintDocumentAdapter
import android.print.PrintJob
import android.print.PrintManager
import android.webkit.WebView
import android.webkit.WebViewClient
import com.hazelpay.merchant.tap2pay.model.ReceiptData

object PrinterUtils {

    /**
     * Print receipt using Android's standard print preview
     */
    fun printReceipt(
        context: Context,
        receiptData: ReceiptData,
        callback: (Boolean, String?) -> Unit
    ) {
        try {
            // Create HTML content for the receipt
            val htmlContent = generateReceiptHtml(receiptData)

            // Create WebView to render the HTML
            val webView = WebView(context)
            webView.webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)

                    // Create print job
                    createPrintJob(context, webView, callback)
                }
            }

            // Load HTML content
            webView.loadDataWithBaseURL(null, htmlContent, "text/html", "UTF-8", null)

        } catch (e: Exception) {
            callback(false, "Failed to create print preview: ${e.message}")
        }
    }

    private fun createPrintJob(context: Context, webView: WebView, callback: (Boolean, String?) -> Unit) {
        try {
            val printManager = context.getSystemService(Context.PRINT_SERVICE) as PrintManager

            val printAdapter: PrintDocumentAdapter = webView.createPrintDocumentAdapter("Receipt")

            val printAttributes = PrintAttributes.Builder()
                .setMediaSize(PrintAttributes.MediaSize.UNKNOWN_PORTRAIT)
                .setResolution(PrintAttributes.Resolution("receipt", "Receipt", 203, 203))
                .setColorMode(PrintAttributes.COLOR_MODE_MONOCHROME)
                .setMinMargins(PrintAttributes.Margins.NO_MARGINS)
                .build()

            val printJob: PrintJob = printManager.print(
                "Receipt_${System.currentTimeMillis()}",
                printAdapter,
                printAttributes
            )

            callback(true, "Print preview opened successfully")

        } catch (e: Exception) {
            callback(false, "Failed to open print preview: ${e.message}")
        }
    }

    private fun generateReceiptHtml(receiptData: ReceiptData): String {
        return """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <style>
                    @page {
                        size: 80mm auto;
                        margin: 0;
                    }
                    body {
                        font-family: 'Courier New', monospace;
                        font-size: 14px;
                        line-height: 1.3;
                        margin: 0;
                        padding: 8mm;
                        width: 100%;
                        box-sizing: border-box;
                    }
                    .center {
                        text-align: center;
                    }
                    .separator {
                        border-top: 1px dashed #000;
                        margin: 8px 0;
                        width: 100%;
                    }
                    .total {
                        font-weight: bold;
                        font-size: 16px;
                    }
                    .status {
                        font-weight: bold;
                        text-align: center;
                        margin: 8px 0;
                        font-size: 14px;
                    }
                    .header-line {
                        text-align: center;
                        margin: 2px 0;
                    }
                    .detail-line {
                        margin: 3px 0;
                        word-wrap: break-word;
                    }
                    .company-name {
                        font-weight: bold;
                        font-size: 16px;
                    }
                </style>
            </head>
            <body>
                <div class="center">
                    <div class="header-line">================================</div>
                    <div class="header-line company-name">${receiptData.companyName}</div>
                    <div class="header-line">ID: ${receiptData.companyId}</div>
                    <div class="header-line">${receiptData.address}</div>
                    <div class="header-line">================================</div>
                </div>

                <br>

                ${if (!receiptData.vatPercentage.isNullOrEmpty() && receiptData.vatPercentage != "0%")
                    "<div class=\"detail-line\">VAT (${receiptData.vatPercentage}): ${receiptData.currency} ${receiptData.vatAmount}</div>"
                else ""}

                ${if (!receiptData.tipPercentage.isNullOrEmpty() && receiptData.tipPercentage != "0%")
                    "<div class=\"detail-line\">Tip (${receiptData.tipPercentage}): ${receiptData.currency} ${receiptData.tipAmount}</div>"
                else ""}

                <div class="detail-line">Subtotal: ${receiptData.currency} ${receiptData.subtotal}</div>

                <div class="separator"></div>

                <div class="total">TOTAL: ${receiptData.currency} ${receiptData.total}</div>

                <div class="status">*** ${receiptData.status} ***</div>

                <br>

                <div class="detail-line">Date/Time: ${receiptData.dateTime}</div>
                <div class="detail-line">Card: ${receiptData.cardMask}</div>
                <div class="detail-line">Payment Method: ${receiptData.paymentMethod}</div>
                <div class="detail-line">TID: ${receiptData.transactionId}</div>
                <div class="detail-line">MID: ${receiptData.companyId}</div>
                <div class="detail-line">Terminal ID: ${receiptData.terminalId}</div>
                <div class="detail-line">RRN: ${receiptData.rrn}</div>
                <div class="detail-line">Auth Code: ${receiptData.authCode}</div>
                <div class="detail-line">AID: ${receiptData.aid}</div>

                ${if (!receiptData.tvr.isNullOrEmpty()) "<div class=\"detail-line\">TVR: ${receiptData.tvr}</div>" else ""}

                <br>

                <div class="center">
                    <div class="header-line">================================</div>
                    <div class="header-line">Thank you for your business!</div>
                    <br>
                    <div class="header-line">Processed by HazelsOne</div>
                    <div class="header-line">(a product of eftaapay)</div>
                    <div class="header-line">================================</div>
                </div>
            </body>
            </html>
        """.trimIndent()
    }
}
